{"ListOfPolDet": [{"MainBenefit": "Outpatient Benefits", "MainBenefitEN": "Outpatient Benefits", "Coverage": [{"CovNo": "1", "CovNoEN": "1", "CovDesc": "ผู้ป่วยนอก", "CovDescEN": "ผู้ป่วยนอก", "CovLimit": "2,000", "CovLimitEN": "2,000 ", "CovUtilized": " (0 ครั้ง)", "CovUtilizedEN": " (0 visit)"}, {"CovNo": "หมายเหตุ", "CovNoEN": "Remarks", "CovDesc": "1. (1) สูงสุดไม่เกิน 14,000 บาท/ปี", "CovDescEN": "1. Max limit (1) = 14,000 baht /year", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "2. (1) สูงสุดไม่เกิน 10 ครั้ง/ปี", "CovDescEN": "2. Max limit (1) = 10 visit(s)/year", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}]}, {"MainBenefit": "Inpatient Benefits", "MainBenefitEN": "Inpatient Benefits", "Coverage": [{"CovNo": "1", "CovNoEN": "1", "CovDesc": "ค่าห้องผู้ป่วยปกติ และค่าอาหาร", "CovDescEN": "ค่าห้องผู้ป่วยปกติ และค่าอาหาร", "CovLimit": "2,500", "CovLimitEN": "2,500 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "2", "CovNoEN": "2", "CovDesc": "ค่าห้องผู้ป่วยหนัก และค่าอาหาร", "CovDescEN": "ค่าห้องผู้ป่วยหนัก และค่าอาหาร", "CovLimit": "5,000", "CovLimitEN": "5,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "3", "CovNoEN": "3", "CovDesc": "ค่ารักษาพยาบาลทั่วไป", "CovDescEN": "ค่ารักษาพยาบาลทั่วไป", "CovLimit": "125,000", "CovLimitEN": "125,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "4", "CovNoEN": "4", "CovDesc": "ค่ารถพยาบาล", "CovDescEN": "ค่ารถพยาบาล", "CovLimit": "125,000", "CovLimitEN": "125,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "5", "CovNoEN": "5", "CovDesc": "ค่าแพทย์ที่ปรึกษา กรณีไม่ผ่าตัด", "CovDescEN": "ค่าแพทย์ที่ปรึกษา กรณีไม่ผ่าตัด", "CovLimit": "125,000", "CovLimitEN": "125,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "6", "CovNoEN": "6", "CovDesc": "ยากลับบ้าน", "CovDescEN": "ยากลับบ้าน", "CovLimit": "2,000", "CovLimitEN": "2,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "7", "CovNoEN": "7", "CovDesc": "ค่าแพทย์ผ่าตัดและหัตถการ", "CovDescEN": "ค่าแพทย์ผ่าตัดและหัตถการ", "CovLimit": "125,000", "CovLimitEN": "125,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "8", "CovNoEN": "8", "CovDesc": "ค่าแพทย์ที่ปรึกษา กรณีผ่าตัด", "CovDescEN": "ค่าแพทย์ที่ปรึกษา กรณีผ่าตัด", "CovLimit": "125,000", "CovLimitEN": "125,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "9", "CovNoEN": "9", "CovDesc": "ค่าแพทย์เยี่ยมไข้", "CovDescEN": "ค่าแพทย์เยี่ยมไข้", "CovLimit": "275,000", "CovLimitEN": "275,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "หมายเหตุ", "CovNoEN": "Remarks", "CovDesc": "1. (3)+(4)+(5)+(6) สูงสุดไม่เกิน 250,000 บาท/ครั้ง", "CovDescEN": "1. Max limit (3)+(4)+(5)+(6) = 250,000 baht /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "2. (1)+(2) สูงสุดไม่เกิน 90 วัน/ครั้ง", "CovDescEN": "2. Max limit (1)+(2) = 90 day(s) /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "3. (7)+(8) สูงสุดไม่เกิน 125,000 บาท/ครั้ง", "CovDescEN": "3. Max limit (7)+(8) = 125,000 baht /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "4. ผลประโยชน์ IPD สูงสุดไม่เกิน 275,000 บาท/ปี", "CovDescEN": "4. Max limit IPD = 275,000 baht/year", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "5. (9) รวมกันไม่เกิน 90 วัน/ครั้ง", "CovDescEN": "5. Max limit (9) = 90 day(s) /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "6. (2) รวมกันไม่เกิน 15 วัน/ครั้ง", "CovDescEN": "6. Max limit (2) = 15 day(s) /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}]}, {"MainBenefit": "PA", "MainBenefitEN": "PA", "Coverage": [{"CovNo": "1", "CovNoEN": "1", "CovDesc": "ค่ารักษาพยาบาลจากอุบัติเหตุทั่วไป (ไม่รวมมอเตอร์ไซด์)", "CovDescEN": "ค่ารักษาพยาบาลจากอุบัติเหตุทั่วไป (ไม่รวมมอเตอร์ไซด์)", "CovLimit": "20,000", "CovLimitEN": "20,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "2", "CovNoEN": "2", "CovDesc": "ค่ารักษาพยาบาลจากอุบัติเหตุมอเตอร์ไซด์", "CovDescEN": "ค่ารักษาพยาบาลจากอุบัติเหตุมอเตอร์ไซด์", "CovLimit": "20,000", "CovLimitEN": "20,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}]}, {"MainBenefit": "Dental", "MainBenefitEN": "Dental", "Coverage": [{"CovNo": "1", "CovNoEN": "1", "CovDesc": "ทันตกรรม", "CovDescEN": "ทันตกรรม", "CovLimit": "3,000", "CovLimitEN": "3,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}]}, {"MainBenefit": "Flexible Benefits", "MainBenefitEN": "Flexible Benefits", "Coverage": [{"CovNo": "1", "CovNoEN": "1", "CovDesc": "ค่าส่งเสริมสุขภาพสายตา", "CovDescEN": "ค่าส่งเสริมสุขภาพสายตา", "CovLimit": "3,000", "CovLimitEN": "3,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "2", "CovNoEN": "2", "CovDesc": "คลาสฟิตเนส เครื่องออกกำลังกาย อุปกรณ์กีฬา", "CovDescEN": "คลาสฟิตเนส เครื่องออกกำลังกาย อุปกรณ์กีฬา", "CovLimit": "3,000", "CovLimitEN": "3,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "3", "CovNoEN": "3", "CovDesc": "ค่ารักษาแพทย์ทางเลือก", "CovDescEN": "ค่ารักษาแพทย์ทางเลือก", "CovLimit": "3,000", "CovLimitEN": "3,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "4", "CovNoEN": "4", "CovDesc": "อุปกรณ์การทำงาน", "CovDescEN": "อุปกรณ์การทำงาน", "CovLimit": "3,000", "CovLimitEN": "3,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "หมายเหตุ", "CovNoEN": "Remarks", "CovDesc": "1. (1)+(2)+(3)+(4) สูงสุดไม่เกิน 3,000 บาท/ปี", "CovDescEN": "1. Max limit (1)+(2)+(3)+(4) = 3,000 baht /year", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}]}], "ListOfPolClaim": [{"ClmInsurerCode": "INS202200006", "ClmInsurerTH": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "ClmInsurerEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "ClmCompanyCode": "COM202200080", "ClmCompanyTH": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "ClmCompanyEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "ClmCardType": "Group Insurance", "ClmPolNo": "BVTPA_2024", "ClmNo": "C20250001714", "ClmSource": "Reimbursement", "ClmType": "Accident", "ClmDiagCode": "A25.9", "ClmDiagTH": "ไข้ที่เกิดจากหนู ที่มิได้ระบุรายละเอียด", "ClmDiagEN": "Rat-bite fever,unspecified                                                     ", "ClmStatus": "Open", "ClmStatusTxt": "อยู่ระหว่างดำเนินการ", "ClmStatusTxtEN": "In Progress", "ClmVisitDate": "23/05/2025", "ClmDischargeDate": "23/05/2025", "ClmIncurredAmt": "1000000", "ClmPayable": "2000", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600944", "ClmProviderTH": "พญาไท 2 BDMS", "ClmProviderEN": "PHYATHAI 2 HOSPITAL"}, {"ClmInsurerCode": "INS202200006", "ClmInsurerTH": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "ClmInsurerEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "ClmCompanyCode": "COM202200080", "ClmCompanyTH": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "ClmCompanyEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "ClmCardType": "Group Insurance", "ClmPolNo": "BVTPA_2024", "ClmNo": "C20250001706", "ClmSource": "Credit", "ClmType": "Accident", "ClmDiagCode": "", "ClmDiagTH": "", "ClmDiagEN": "", "ClmStatus": "Open", "ClmStatusTxt": "อยู่ระหว่างดำเนินการ", "ClmStatusTxtEN": "In Progress", "ClmVisitDate": "02/05/2025", "ClmDischargeDate": "02/05/2025", "ClmIncurredAmt": "0", "ClmPayable": "0", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600946", "ClmProviderTH": "พระรามเก้า", "ClmProviderEN": "PRARAM 9 HOSPITAL"}], "ErrorMessage": " "}
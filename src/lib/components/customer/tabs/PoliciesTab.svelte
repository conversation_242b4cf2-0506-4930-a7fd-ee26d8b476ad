<!--
	@component PoliciesTab

	A comprehensive policies and claims management component for the CustomerInfoPanel.
	Provides viewing, filtering, and detailed information about customer insurance policies and claims.

	Features:
	- Policy and claims overview with summary statistics
	- Advanced filtering by status, type, and search
	- Detailed policy and claim information modals
	- Real-time data updates via polling
	- Full accessibility and internationalization support

	@example
	```svelte
	<PoliciesTab
		{customer}
		{access_token}
		platformId={1}
		ticketId={123}
		hideEditButton={false}
		hideAssignmentsHistory={false}
	/>
	```
-->
<script lang="ts">
	import type {
		Customer,
		CustomerPoliciesData,
		Policy,
		Claim,
		PolicyFilters,
		ClaimFilters
	} from '$lib/types/customer';
	import type { PollingConfig } from '$lib/types/polling';
	import { onMount, onDestroy } from 'svelte';
	import { t } from '$lib/stores/i18n';
	import { formatTimestampDMY } from '$lib/utils';
	import { PollingService } from '$lib/services/pollingService';
	import {
		Button,
		Timeline,
		TimelineItem,
		Dropdown,
		DropdownItem,
		Tooltip
	} from 'flowbite-svelte';
	import {
		ExclamationCircleOutline,
		CloseCircleOutline,
		ClipboardListOutline,
		AdjustmentsHorizontalSolid,
		RefreshOutline,
		CheckCircleOutline,
		HourglassOutline,
		UserOutline,
		BuildingOutline,
		CalendarMonthOutline,
		ShieldCheckOutline,
		DownloadOutline,
		EyeOutline
	} from 'flowbite-svelte-icons';


	// Import API services
	import { services } from '$src/lib/api/features';

	// Import modal components
	import ModalPortal from '$lib/components/common/ModalPortal.svelte';
	import PolicyDetailModal from '$lib/components/customer/modals/PolicyDetailModal.svelte';

	// Props following existing tab component patterns
	/** Customer data object containing basic customer information */
	export let customer: Customer;
	/** Platform identifier for multi-platform support (optional) */
	export let platformId: number | null = null;
	/** Authentication token for API requests */
	export let access_token: string;
	/** Associated ticket ID for context (optional) */
	export let ticketId: number | null = null;
	/** Hide edit functionality when true (optional) */
	export let hideEditButton: boolean = false;
	/** Hide assignment history when true (optional) */
	export let hideAssignmentsHistory: boolean = false;

	// Component state
	let loading = false;
	let error = '';
	let policiesData: CustomerPoliciesData | null = null;
	let filteredPolicies: Policy[] = [];
	let filteredClaims: Claim[] = [];

	// Enhanced error handling and workflow status
	let workflowStatus: 'idle' | 'executing' | 'success' | 'error' | 'fallback' = 'idle';
	let workflowError: string = '';
	let workflowStep: string = '';
	let retryCount = 0;
	let maxRetries = 3;
	let canRetry = false;
	let isUsingFallback = false;

	// Polling service instance
	let pollingService: PollingService;

	// Data refresh state variables
	let refreshingPolicies = false;

	// Initial data loading state management
	let initialDataLoaded = false;
	let initialDataLoading = false;

	// Track current customer ID to reset flags when customer changes
	let currentCustomerId: number | null = null;

	// Modal states
	let policyDetailModalOpen = false;
	let selectedPolicy: Policy | null = null;
	let selectedClaim: Claim | null = null;

	// Filter states (updated to match my-policy.svelte)
	let showFilters = false;
	let searchQuery = '';
	let selectedStatuses: Set<string> = new Set(['All']);
	let selectedTypes: Set<string> = new Set(['All']);
	let startDateFilter: Date | null = null;
	let endDateFilter: Date | null = null;
	let dateRange = '';

	// Legacy filter states for claims view
	let policyFilters: PolicyFilters = {
		status: [],
		type: [],
		search_query: ''
	};
	let claimFilters: ClaimFilters = {
		status: [],
		type: [],
		search_query: ''
	};

	// View state
	let activeView: 'policies' | 'claims' = 'policies';

	// Policy status order for sorting (from my-policy.svelte)
	const statusOrder = ['active', 'pending', 'nearly_expired', 'expired', 'cancelled', 'inactive'];



	// Debug logging for filtered policies
	$: {
		console.log('Policies data state:', {
			filteredPoliciesCount: filteredPolicies.length,
			totalPoliciesData: policiesData?.policies?.length || 0,
			loading,
			error
		});
		
		if (filteredPolicies.length > 0) {
			console.log('First policy sample:', {
				id: filteredPolicies[0].id,
				number: filteredPolicies[0].policy_number,
				name: filteredPolicies[0].product?.name
			});
		}
	}

	// Import the policies cache service
	import { policiesCacheService } from '$lib/services/policiesCacheService';



	// Convert date range string to Date objects for filtering logic
	$: {
		if (dateRange && typeof dateRange === 'string' && dateRange.includes(' ~ ')) {
			const [startStr, endStr] = dateRange.split(' ~ ');
			startDateFilter = startStr ? new Date(startStr) : null;
			endDateFilter = endStr ? new Date(endStr) : null;
		} else {
			startDateFilter = null;
			endDateFilter = null;
		}
	}

	// Enhanced filter logic for policies (from my-policy.svelte)
	$: if (policiesData && activeView === 'policies') {
		filteredPolicies = policiesData.policies.filter((policy: Policy) => {
			// Search filter
			const searchMatch =
				!searchQuery ||
				policy.product?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
				policy.product?.product_type?.toLowerCase().includes(searchQuery.toLowerCase());

			// Status filter
			const statusMatch = selectedStatuses.has('All') || selectedStatuses.has(policy.policy_status);

			// Type filter
			const typeMatch = selectedTypes.has('All') || selectedTypes.has(policy.product?.product_type);

			// Date range filter
			let dateMatch = true;
			if (startDateFilter || endDateFilter) {
				const policyStartDate = policy.start_date ? new Date(policy.start_date) : null;
				const policyEndDate = policy.end_date ? new Date(policy.end_date) : null;

				if (startDateFilter && policyStartDate) {
					dateMatch = dateMatch && policyStartDate >= startDateFilter;
				}
				if (endDateFilter && policyEndDate) {
					dateMatch = dateMatch && policyEndDate <= endDateFilter;
				}
			}

			return searchMatch && statusMatch && typeMatch && dateMatch;
		});

		// Sort filtered policies by status
		filteredPolicies = filteredPolicies.sort((a: Policy, b: Policy) => {
			const aIndex = statusOrder.indexOf(a.policy_status.toLowerCase());
			const bIndex = statusOrder.indexOf(b.policy_status.toLowerCase());
			return aIndex - bIndex;
		});
	}

	// Available filter options
	$: availableStatuses = policiesData
		? ([
				'All',
				...Array.from(new Set(policiesData.policies.map((p: Policy) => p.policy_status)))
			] as string[])
		: ['All'];

	$: availableTypes = policiesData
		? ([
				'All',
				...Array.from(
					new Set(policiesData.policies.map((p: Policy) => p.product?.product_type).filter(Boolean))
				)
			] as string[])
		: ['All'];

	// Reactive statement that properly tracks all filter dependencies
	$: activeFilterCount = (() => {
		let count = 0;
		if (searchQuery && searchQuery.trim() !== '') count++;
		if (!selectedStatuses.has('All')) count++;
		if (!selectedTypes.has('All')) count++;
		if (startDateFilter || endDateFilter) count++;
		return count;
	})();

	/**
	 * Loads customer policies and claims data with platform-specific caching
	 * Enhanced with 1-minute cache expiration and better error handling
	 * Updates component state with loaded data and initializes filtered arrays
	 *
	 * @throws {Error} When customer ID or access token is missing
	 */
	async function loadPoliciesData() {
		try {
			loading = true;
			error = '';
			workflowStatus = 'executing';
			workflowError = '';
			workflowStep = 'Initializing...';
			isUsingFallback = false;

			if (!customer?.customer_id) {
				throw new Error('No customer ID provided');
			}

			if (!access_token) {
				throw new Error('No access token provided');
			}

			// Check cache first
			workflowStep = 'Checking cache...';
			const cachedData = policiesCacheService.get(customer.customer_id, platformId);

			if (cachedData) {
				console.log('Using cached policies data for customer:', customer.customer_id, 'platform:', platformId);
				policiesData = cachedData;
				workflowStatus = 'success';
				workflowStep = 'Data loaded from cache';
				retryCount = 0;
				canRetry = false;

				// Initialize filtered data
				filteredPolicies = policiesData.policies || [];
				filteredClaims = policiesData.claims || [];
				return;
			}

			// Cache miss - fetch from API
			workflowStep = 'Fetching from API...';
			console.log('Cache miss - fetching policies data for customer:', customer.customer_id, 'platform:', platformId);

			const result = await services.customers.getCustomerPoliciesAndClaims(
				customer.customer_id.toString(),
				access_token
			);

			if (
				result.res_status === 200 &&
				result.customer_policies &&
				result.customer_policies.length > 0
			) {
				workflowStep = 'Processing raw policy data...';
				// Store raw API response data without transformation
				const rawData = result.customer_policies[0];

				console.log('Raw policy workflow data received:', {
					customer_id: rawData.customer_id,
					execution_id: rawData.execution_id,
					step3_policies: rawData.step3_data.ListOfPolicyListSocial.length,
					step4_policy_details: rawData.step4_data.ListOfPolDet.length,
					step4_claims: rawData.step4_data.ListOfPolClaim.length,
					execution_time: rawData.execution_metadata.total_execution_time_ms
				});

				// Create a simplified structure for the component to use raw data
				policiesData = {
					customer_id: parseInt(rawData.customer_id),
					customer_name: `Customer ${rawData.customer_id}`,
					customer_email: `customer${rawData.customer_id}@example.com`,
					policies: [], // Will be populated from raw data if needed
					claims: [], // Will be populated from raw data if needed
					statistics: {
						total_policies: rawData.step3_data.ListOfPolicyListSocial.length,
						active_policies: 0,
						expired_policies: 0,
						pending_policies: 0,
						cancelled_policies: 0,
						waiting_period_policies: 0,
						nearly_expired_policies: 0,
						total_premium_amount: 0,
						total_coverage_amount: 0,
						average_premium: 0,
						total_claims: rawData.step4_data.ListOfPolClaim.length,
						active_claims: 0,
						approved_claims: 0,
						rejected_claims: 0,
						total_claims_amount: 0,
						total_paid_amount: 0,
						policy_type_breakdown: {
							LIFE: 0,
							HEALTH: 0,
							AUTO: 0,
							PROPERTY: 0,
							TRAVEL: 0,
							DISABILITY: 0,
							CRITICAL_ILLNESS: 0
						},
						recent_policies: 0,
						recent_claims: 0
					},
					last_updated: new Date().toISOString(),
					// Store raw data for direct access
					raw_data: rawData
				};

				// Cache the successful response
				policiesCacheService.set(customer.customer_id, platformId, policiesData);

				workflowStatus = 'success';
				workflowStep = 'Workflow completed successfully';
				retryCount = 0;
				canRetry = false;
				console.log('Policy workflow executed successfully and cached');
				console.log('Policies data:', policiesData);
			} else {
				throw new Error(result.error_msg || 'Failed to load policies from API');
			}

			// Initialize filtered data
			if (policiesData) {
				filteredPolicies = policiesData.policies || [];
				filteredClaims = policiesData.claims || [];
			}
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to load policies data';
			workflowStatus = 'error';
			workflowError = error;
			workflowStep = 'API request failed';
			canRetry = retryCount < maxRetries;
			console.error('Error loading policies:', err);
		} finally {
			loading = false;
		}
	}

	/**
	 * Retry the policy workflow execution
	 * Increments retry count and attempts to reload data (bypasses cache)
	 */
	async function retryWorkflow() {
		if (retryCount >= maxRetries) {
			console.warn('Maximum retry attempts reached');
			return;
		}

		retryCount++;
		console.log(`Retrying policy workflow (attempt ${retryCount}/${maxRetries})`);

		// Force refresh by clearing cache for retry
		if (customer?.customer_id) {
			policiesCacheService.forceRefresh(customer.customer_id, platformId);
		}

		await loadPoliciesData();
	}

	/**
	 * Applies current filter criteria to the policies array
	 * Filters by search query, status, and policy type
	 * Updates the filteredPolicies array with matching results
	 */
	function applyPolicyFilters() {
		if (!policiesData) return;

		filteredPolicies = policiesData.policies.filter((policy) => {
			// Search filter
			if (searchQuery.trim()) {
				const query = searchQuery.toLowerCase();
				const matchesSearch =
					policy.policy_number.toLowerCase().includes(query) ||
					policy.product.name.toLowerCase().includes(query) ||
					policy.product.product_type.toLowerCase().includes(query);
				if (!matchesSearch) return false;
			}

			// Status filter
			if (policyFilters.status && policyFilters.status.length > 0) {
				if (!policyFilters.status.includes(policy.policy_status)) return false;
			}

			// Type filter
			if (policyFilters.type && policyFilters.type.length > 0) {
				if (!policyFilters.type.includes(policy.product.product_type)) return false;
			}

			return true;
		});
	}

	// Filter claims based on current filters
	function applyClaimFilters() {
		if (!policiesData) return;

		filteredClaims = policiesData.claims.filter((claim) => {
			// Search filter
			if (searchQuery.trim()) {
				const query = searchQuery.toLowerCase();
				const matchesSearch =
					claim.claim_number.toLowerCase().includes(query) ||
					claim.description.toLowerCase().includes(query) ||
					claim.claim_type.toLowerCase().includes(query);
				if (!matchesSearch) return false;
			}

			// Status filter
			if (claimFilters.status && claimFilters.status.length > 0) {
				if (!claimFilters.status.includes(claim.claim_status)) return false;
			}

			// Type filter
			if (claimFilters.type && claimFilters.type.length > 0) {
				if (!claimFilters.type.includes(claim.claim_type)) return false;
			}

			return true;
		});
	}

	// Apply filters when search query or filters change
	$: if (searchQuery !== undefined) {
		if (activeView === 'policies') {
			applyPolicyFilters();
		} else {
			applyClaimFilters();
		}
	}

	// Filter helper functions (from my-policy.svelte)
	function toggleFilter(value: string, selectedSet: Set<string>): Set<string> {
		const newSet = new Set(selectedSet);

		if (value === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(value)) {
				newSet.delete(value);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(value);
			}
		}

		return newSet;
	}

	function clearAllFilters() {
		searchQuery = '';
		selectedStatuses = new Set(['All']);
		selectedTypes = new Set(['All']);
		startDateFilter = null;
		endDateFilter = null;
		dateRange = '';
	}

	// Enhanced clear filters function with keyboard support
	function handleClearFilters(event: KeyboardEvent | MouseEvent) {
		// Handle keyboard events (Enter and Space)
		if (event instanceof KeyboardEvent) {
			if (event.key !== 'Enter' && event.key !== ' ') {
				return;
			}
			event.preventDefault();
		}
		clearAllFilters();
	}

	// Handle policy card click (updated to work with raw policy data)
	function handlePolicyClick(rawPolicy: any) {
		console.log('Policy card clicked:', {
			policyNumber: rawPolicy.PolNo,
			policyName: rawPolicy.PlanName,
			memberCode: rawPolicy.MemberCode
		});

		// Convert raw policy to Policy format for modal compatibility
		const convertedPolicy: Policy = {
			id: rawPolicy.PolNo,
			policy_number: rawPolicy.PolNo,
			policy_status: 'active', // Assuming active based on the design
			start_date: rawPolicy.EffFrom,
			end_date: rawPolicy.EffTo,
			premium_amount: 0, // Not available in raw data
			coverage_amount: 0, // Not available in raw data
			currency: 'THB',
			product: {
				id: rawPolicy.PlanCode,
				name: rawPolicy.PlanName,
				product_type: rawPolicy.CardType,
				description: `${rawPolicy.PlanName} - ${rawPolicy.CardType}`
			},
			customer_id: parseInt(rawPolicy.CitizenID) || 0,
			created_at: new Date().toISOString(),
			updated_at: new Date().toISOString()
		};

		selectedPolicy = convertedPolicy;
		console.log('Selected policy set:', selectedPolicy);

		policyDetailModalOpen = true;
		console.log('🚪 Modal open state set to:', policyDetailModalOpen);

		// Additional check to verify state changes
		setTimeout(() => {
			console.log('Modal state after timeout:', {
				policyDetailModalOpen,
				selectedPolicy: selectedPolicy?.id,
				modalElement: document.getElementById('policy-detail-modal-portal')
			});
		}, 100);
	}

	// Handle keyboard events for accessibility
	function handlePolicyKeydown(event: KeyboardEvent, rawPolicy: any) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			handlePolicyClick(rawPolicy);
		}
	}

	/**
	 * Opens the policy detail modal with the selected policy
	 * Sets the selected policy and opens the modal for detailed view
	 *
	 * @param {Policy} policy - The policy object to display in detail
	 */
	function openPolicyDetail(policy: Policy) {
		selectedPolicy = policy;
		policyDetailModalOpen = true;
	}

	// Close policy detail modal
	function closePolicyDetail() {
		selectedPolicy = null;
		policyDetailModalOpen = false;
	}

	// Status color classes for badges with comprehensive status support
	type StatusKey = 'active' | 'inactive' | 'nearly_expired' | 'expired' | 'pending' | 'cancelled' | 'suspended' | 'waiting_period';
	const statusColors: Record<StatusKey, string> & Record<string, string> = {
		active: 'bg-green-100 text-green-500 border-green-200',
		pending: 'bg-blue-100 text-blue-500 border-blue-200',
		nearly_expired: 'bg-yellow-100 text-yellow-500 border-yellow-200',
		expired: 'bg-red-100 text-red-500 border-red-200',
		inactive: 'bg-gray-100 text-gray-500 border-gray-200',
		cancelled: 'bg-yellow-100 text-yellow-500 border-yellow-200',
		suspended: 'bg-orange-100 text-orange-500 border-orange-200',
		waiting_period: 'bg-yellow-100 text-yellow-600 border-yellow-200'
	};

	// Get status color class
	function getStatusColorClass(status: string): string {
		const statusKey = status.toLowerCase() as StatusKey;
		return statusColors[statusKey] || statusColors.inactive;
	}

	// Function to format date (from my-policy.svelte)
	const formatTimestampDMYDraft = (timestamp: string) => {
		const displayCreated = new Date(timestamp);

		// Format each part separately
		const day = displayCreated.getDate().toString().padStart(2, '0');
		const month = displayCreated.toLocaleString('en-US', { month: 'short' });
		const year = displayCreated.getFullYear();

		// Combine in desired format
		return `${day} ${month} ${year}`;
	};

	// Format currency
	function formatCurrency(amount: number, currency: string = 'THB'): string {
		return new Intl.NumberFormat('th-TH', {
			// style: 'currency',
			currency: currency
		}).format(amount);
	}

	// Format date
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	// Helper functions to generate realistic placeholder data
	function getCertificateNumber(policyNumber: string): string {
		const prefix = 'CERT-';
		const suffix = policyNumber.split('-').slice(0, 2).join('-');
		return prefix + suffix;
	}

	function getInsuranceCardNumber(policyNumber: string): string {
		const prefix = 'IC-';
		const randomNum = Math.floor(Math.random() * 999999).toString().padStart(6, '0');
		return prefix + randomNum;
	}

	function getAgentNumber(policyNumber: string): string {
		const cityCode = ['BKK', 'CNX', 'HDY', 'KHN', 'PKT'][Math.floor(Math.random() * 5)];
		const agentNum = Math.floor(Math.random() * 999).toString().padStart(3, '0');
		return `AGT-${agentNum}-${cityCode}`;
	}

	function getMemberType(productType: string): string {
		const memberTypes = {
			'HEALTH': 'Individual Member',
			'LIFE': 'Primary Insured',
			'AUTO': 'Policy Holder',
			'TRAVEL': 'Traveler',
			'PROPERTY': 'Property Owner',
			'DISABILITY': 'Benefit Recipient',
			'CRITICAL_ILLNESS': 'Covered Person'
		};
		return memberTypes[productType as keyof typeof memberTypes] || 'Member';
	}

	function getMemberCardType(productType: string): string {
		const cardTypes = {
			'HEALTH': 'Gold Health Card',
			'LIFE': 'Premium Life Card',
			'AUTO': 'Auto Insurance Card',
			'TRAVEL': 'Travel Card',
			'PROPERTY': 'Property Coverage Card',
			'DISABILITY': 'Disability Benefits Card',
			'CRITICAL_ILLNESS': 'Critical Care Card'
		};
		return cardTypes[productType as keyof typeof cardTypes] || 'Standard Card';
	}

	function getInsuranceCompany(productType: string): string {
		const companies = {
			'HEALTH': 'Health Insurance B Ltd.',
			'LIFE': 'Life Insurance A Ltd.',
			'AUTO': 'Auto Insurance C Ltd.',
			'TRAVEL': 'Travel Insurance D Ltd.',
			'PROPERTY': 'Property Insurance E Ltd.',
			'DISABILITY': 'Disability Insurance F Ltd.',
			'CRITICAL_ILLNESS': 'Critical Care Insurance G Ltd.'
		};
		return companies[productType as keyof typeof companies] || 'General Insurance Ltd.';
	}

	function getPlanCode(policyNumber: string, productType: string): string {
		const typeCode = productType.substring(0, 3);
		const year = policyNumber.includes('2024') ? '2024' : '2023';
		return `${typeCode}-PLAN-${year}`;
	}

	function getPlanNumber(policyNumber: string): string {
		const parts = policyNumber.split('-');
		return `PLN-${parts[1] || '000'}-${parts[2] || '2024'}`;
	}

	// Initialize data loading and polling
	async function initializeDataAndPolling() {
		try {
			initialDataLoading = true;

			// Load initial data
			await loadPoliciesData();

			// Initialize polling after successful data load
			// initializePolling();

			initialDataLoaded = true;
		} catch (err) {
			console.error('Failed to initialize policies data and polling:', err);
		} finally {
			initialDataLoading = false;
		}
	}

	// Initialize polling service
	function initializePolling() {
		pollingService = PollingService.getInstance();

		// Register policies polling
		const policiesConfig: PollingConfig = {
			endpoint: `/customer/api/customers/${customer.customer_id}/policies/`,
			interval: 30000, // 30 seconds
			customFetcher: () => loadPoliciesData(),
			onError: (error) => {
				console.error('Policies polling error:', error);
			}
		};
		pollingService.registerEndpoint('customer-policies', policiesConfig);
	}

	// Refresh policies data (force cache refresh)
	async function refreshPoliciesData() {
		try {
			refreshingPolicies = true;
			// Force refresh by clearing cache first
			if (customer?.customer_id) {
				policiesCacheService.forceRefresh(customer.customer_id, platformId);
			}
			// Reset workflow state for fresh attempt
			workflowStatus = 'idle';
			workflowError = '';
			isUsingFallback = false;
			retryCount = 0;
			await loadPoliciesData();
		} catch (err) {
			console.error('Failed to refresh policies data:', err);
		} finally {
			refreshingPolicies = false;
		}
	}

	// Debug logging for customer changes
	$: {
		console.log('Customer prop changed:', {
			customerId: customer?.customer_id,
			customerName: customer?.first_name || customer?.name,
			access_token: !!access_token,
			initialDataLoaded,
			initialDataLoading
		});
	}

	// Reactive data loading when customer becomes available
	$: if (customer && customer.customer_id && access_token && !initialDataLoaded && !initialDataLoading) {
		console.log('Reactive data loading triggered for customer:', customer.customer_id);
		
		// Reset flags if customer has changed
		if (currentCustomerId !== null && currentCustomerId !== customer.customer_id) {
			console.log('Customer changed, resetting flags');
			initialDataLoaded = false;
			initialDataLoading = false;
		}
		
		currentCustomerId = customer.customer_id;
		initializeDataAndPolling();
	}

	// Initialize component
	onMount(() => {
		console.log('PoliciesTab onMount triggered:', {
			customer: !!customer,
			customerId: customer?.customer_id,
			access_token: !!access_token,
			initialDataLoaded,
			initialDataLoading
		});
		
		// Only initialize if not already done and not in progress
		if (
			customer &&
			customer.customer_id &&
			access_token &&
			!initialDataLoaded &&
			!initialDataLoading
		) {
			console.log('Starting data initialization...');
			initializeDataAndPolling();
		} else {
			console.log('Skipping initialization:', {
				hasCustomer: !!customer,
				hasCustomerId: !!customer?.customer_id,
				hasToken: !!access_token,
				alreadyLoaded: initialDataLoaded,
				loading: initialDataLoading
			});
		}
	});

	// Cleanup
	onDestroy(() => {
		if (pollingService) {
			// Clean up polling endpoints
			pollingService.unregisterEndpoint('customer-policies');
		}
		// Reset initialization flags for cleanup
		initialDataLoaded = false;
		initialDataLoading = false;
	});
</script>

<div id="policies-tab-container" class="p-4" data-testid="policies-tab">
	{#if loading}
		<div class="py-8 text-center">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
			<p class="mt-2 text-sm text-gray-500">{t('loading')}</p>
			{#if workflowStep}
				<p class="mt-1 text-xs text-gray-400">{workflowStep}</p>
			{/if}
		</div>
	{:else if workflowStatus === 'error' && error}
		<div class="py-8 text-center">
			<ExclamationCircleOutline class="mx-auto h-12 w-12 text-red-400" />
			<h3 class="mt-2 text-sm font-medium text-gray-900">{t('error_loading_policies')}</h3>
			<p class="mt-1 text-sm text-gray-500">{error}</p>
			{#if workflowError}
				<p class="mt-1 text-xs text-gray-400">Workflow Error: {workflowError}</p>
			{/if}
			<div class="mt-4 space-x-2">
				<Button on:click={loadPoliciesData} color="blue">
					{t('try_again')}
				</Button>
				{#if canRetry}
					<Button on:click={retryWorkflow} color="alternative">
						Retry Workflow ({retryCount}/{maxRetries})
					</Button>
				{/if}
			</div>
		</div>
	{:else if !policiesData || policiesData.raw_data.step3_data.ListOfPolicyListSocial.length === 0}
		<div class="mt-8 text-center text-gray-500">
			<ClipboardListOutline class="mx-auto h-12 w-12 text-gray-400" />
			<h3 class="mt-2 text-sm font-medium text-gray-400">{t('policy_not_available')}</h3>
			<!-- <p class="mt-1 text-sm text-gray-500">{t('no_policies_description')}</p> -->
		</div>
	{:else}
		<!-- Workflow Status Indicator -->
		{#if isUsingFallback}
			<div class="mb-4 rounded-lg bg-yellow-50 border border-yellow-200 p-4">
				<div class="flex items-center">
					<ExclamationCircleOutline class="h-5 w-5 text-yellow-400 mr-2" />
					<div class="flex-1">
						<h3 class="text-sm font-medium text-yellow-800">Using Fallback Data</h3>
						<p class="text-sm text-yellow-700">
							Policy workflow failed. Displaying sample data for demonstration.
						</p>
						{#if workflowError}
							<p class="text-xs text-yellow-600 mt-1">Error: {workflowError}</p>
						{/if}
					</div>
					{#if canRetry}
						<Button on:click={retryWorkflow} size="sm" color="yellow">
							Retry ({retryCount}/{maxRetries})
						</Button>
					{/if}
				</div>
			</div>
		{:else if workflowStatus === 'success'}
			<div class="mb-4 rounded-lg bg-green-50 border border-green-200 p-3">
				<div class="flex items-center">
					<CheckCircleOutline class="h-4 w-4 text-green-400 mr-2" />
					<p class="text-sm text-green-700">Policy data loaded successfully via workflow</p>
				</div>
			</div>
		{/if}

		<!-- Policy Statistics (from my-policy.svelte) -->
		<div id="policies-tab-statistics" class="mb-4 grid grid-cols-5 gap-2">
			<!-- Total Policies -->
			<div id="total-policies-card" class="flex items-center justify-center rounded-lg bg-gray-100 p-4 cursor-help">
				<ClipboardListOutline class="mr-1 h-5 w-5 text-gray-700" />
				<p class="text-lg font-bold">{policiesData.statistics.total_policies}</p>
			</div>
			<Tooltip triggeredBy="#total-policies-card" placement="top">
				{t('policies_stats_total_tooltip')}
			</Tooltip>

			<!-- Active Policies -->
			<div id="active-policies-card" class="flex items-center justify-center rounded-lg bg-green-100 p-4 cursor-help">
				<CheckCircleOutline class="mr-1 h-5 w-5 text-gray-700" />
				<p class="text-lg font-bold text-green-500">{policiesData.statistics.active_policies}</p>
			</div>
			<Tooltip triggeredBy="#active-policies-card" placement="top">
				{t('policies_stats_active_tooltip')}
			</Tooltip>

			<!-- Waiting Period Policies -->
			<div id="waiting-policies-card" class="flex items-center justify-center rounded-lg bg-blue-100 p-4 cursor-help">
				<HourglassOutline class="mr-1 h-5 w-5 text-blue-700" />
				<p class="text-lg font-bold text-blue-500">
					{policiesData.statistics.waiting_period_policies ||
						policiesData.statistics.pending_policies ||
						0}
				</p>
			</div>
			<Tooltip triggeredBy="#waiting-policies-card" placement="top">
				{t('policies_stats_waiting_tooltip')}
			</Tooltip>

			<!-- Nearly Expired Policies -->
			<!-- <div class="flex items-center justify-center rounded-lg bg-yellow-100 p-4">
				<ExclamationCircleOutline class="mr-1 h-5 w-5 text-yellow-700" />
				<p class="text-lg font-bold text-yellow-500">
					{policiesData.statistics.nearly_expired_policies || 0}
				</p>
			</div> -->

			<!-- Expired Policies -->
			<div id="expired-policies-card" class="flex items-center justify-center rounded-lg bg-red-100 p-4 cursor-help">
				<CloseCircleOutline class="mr-1 h-5 w-5 text-red-700" />
				<p class="text-lg font-bold text-red-500">{policiesData.statistics.expired_policies}</p>
			</div>
			<Tooltip triggeredBy="#expired-policies-card" placement="top">
				{t('policies_stats_expired_tooltip')}
			</Tooltip>

			<!-- Cancelled Policies -->
			<div id="cancelled-policies-card" class="flex items-center justify-center rounded-lg bg-yellow-100 p-4 cursor-help">
				<ExclamationCircleOutline class="mr-1 h-5 w-5 text-yellow-700" />
				<p class="text-lg font-bold text-yellow-500">
					{policiesData.statistics.cancelled_policies || 0}
				</p>
			</div>
			<Tooltip triggeredBy="#cancelled-policies-card" placement="top">
				{t('policies_stats_cancelled_tooltip')}
			</Tooltip>
		</div>

		<!-- Policy Filtering Section (from my-policy.svelte) -->
		<div id="policies-tab-filtering" class="mb-4 space-y-4">
			<!-- Filter Controls -->
			<div class="grid grid-cols-2 gap-3">
				<!-- Status Filter -->
				<Button color="light" class="w-full">
					<AdjustmentsHorizontalSolid class="mr-2 h-4 w-4" />
					{t('policy_filter_by_status')}
					<!-- {#if !selectedStatuses.has('All')}
						<Badge color="blue" class="ml-2">{selectedStatuses.size}</Badge>
					{/if} -->
				</Button>
				<Dropdown class="w-48">
					{#each availableStatuses as status}
						<DropdownItem
							on:click={() => (selectedStatuses = toggleFilter(status, selectedStatuses))}
							class="flex items-center"
						>
							<input type="checkbox" checked={selectedStatuses.has(status)} class="mr-2" readonly />
							{status === 'All' ? t('policy_filter_all_statuses') : t('policy_status_' + status.toLowerCase())}
						</DropdownItem>
					{/each}
				</Dropdown>

				<!-- Type Filter -->
				<Button color="light" class="w-full">
					<AdjustmentsHorizontalSolid class="mr-2 h-4 w-4" />
					{t('policy_filter_by_type')}
					<!-- {#if !selectedTypes.has('All')}
						<Badge color="blue" class="ml-2">{selectedTypes.size}</Badge>
					{/if} -->
				</Button>
				<Dropdown class="w-60">
					{#each availableTypes as type}
						<DropdownItem
							on:click={() => (selectedTypes = toggleFilter(type, selectedTypes))}
							class="flex items-center"
						>
							<input type="checkbox" checked={selectedTypes.has(type)} class="mr-2" readonly />
							{type === 'All' ? t('policy_filter_all_types') : type}
						</DropdownItem>
					{/each}
				</Dropdown>
			</div>

			<!-- Active Filters Display -->
			<!-- {#if activeFilterCount > 0}
				<div class="flex flex-wrap gap-2">
					{#if !selectedStatuses.has('All')}
						{#each Array.from(selectedStatuses) as status}
							<Badge
								color="dark"
								class="flex items-center rounded-full border border-gray-300 bg-gray-200 text-gray-900"
							>
								Status: {t(status.toLowerCase())}
								<button
									on:click={() => (selectedStatuses = toggleFilter(status, selectedStatuses))}
									class="ml-1 hover:text-gray-800"
									aria-label="Remove status filter"
								>
									<CloseOutline class="h-3 w-3" />
								</button>
							</Badge>
						{/each}
					{/if}

					{#if !selectedTypes.has('All')}
						{#each Array.from(selectedTypes) as type}
							<Badge
								color="dark"
								class="flex items-center rounded-full border border-gray-300 bg-gray-200 text-gray-900"
							>
								Type: {type}
								<button
									on:click={() => (selectedTypes = toggleFilter(type, selectedTypes))}
									class="hover:text-dark-800 ml-1"
									aria-label="Remove type filter"
								>
									<CloseOutline class="h-3 w-3" />
								</button>
							</Badge>
						{/each}
					{/if}

					{#if startDateFilter}
						<Badge color="dark" class="flex items-center">
							{t('start_date')}: {startDateFilter.toLocaleDateString()}
							<button
								on:click={() => (startDateFilter = null)}
								class="hover:text-dark-800 ml-1"
								aria-label="Remove start date filter"
							>
								<CloseOutline class="h-3 w-3" />
							</button>
						</Badge>
					{/if}

					{#if endDateFilter}
						<Badge color="dark" class="flex items-center">
							{t('end_date')}: {endDateFilter.toLocaleDateString()}
							<button
								on:click={() => (endDateFilter = null)}
								class="hover:text-dark-800 ml-1"
								aria-label="Remove end date filter"
							>
								<CloseOutline class="h-3 w-3" />
							</button>
						</Badge>
					{/if}
				</div>
			{/if} -->
		</div>

		<!-- View Toggle -->
		<!-- <div id="policies-tab-view-toggle" class="flex items-center justify-between mb-4">
			<div class="flex space-x-1 bg-gray-100 rounded-lg p-1">
				<button
					id="policies-tab-view-policies"
					on:click={() => activeView = 'policies'}
					class="px-4 py-2 text-sm font-medium rounded-md transition-colors
						{activeView === 'policies'
							? 'bg-white text-blue-600 shadow-sm'
							: 'text-gray-500 hover:text-gray-700'}"
				>
					{t('policies')} ({policiesData.policies.length})
				</button>
				<button
					id="policies-tab-view-claims"
					on:click={() => activeView = 'claims'}
					class="px-4 py-2 text-sm font-medium rounded-md transition-colors
						{activeView === 'claims'
							? 'bg-white text-blue-600 shadow-sm'
							: 'text-gray-500 hover:text-gray-700'}"
				>
					{t('claims')} ({policiesData.claims.length})
				</button>
			</div>
		</div> -->

		<!-- Policies Grid -->
		<section
			id="policies-tab-policies-grid"
			class="grid gap-4"
			style="grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));"
			aria-label="Policy cards grid"
		>
			{#if !policiesData?.raw_data?.step3_data?.ListOfPolicyListSocial || policiesData.raw_data.step3_data.ListOfPolicyListSocial.length === 0}
				<div class="col-span-full flex flex-col items-center justify-center py-12 text-center">
					<div class="mb-4 rounded-full bg-gray-100 p-6">
						<AdjustmentsHorizontalSolid class="h-12 w-12 text-gray-400" />
					</div>
					<h3 class="mb-2 text-lg font-medium text-gray-900">No Raw Policy Data Available</h3>
					<p class="text-sm text-gray-500">Raw policy data from the TPA API workflow is not available.</p>
				</div>
			{:else}
				{#each policiesData.raw_data.step3_data.ListOfPolicyListSocial as rawPolicy}
					<div
						class="flex min-h-[480px] w-full
							   flex-col justify-between
							   rounded-lg border
							   border-gray-200 bg-green-50
							   p-6 text-left
							   shadow-sm hover:shadow-md transition-shadow duration-200"
						role="button"
						tabindex="0"
						on:click={() => handlePolicyClick(rawPolicy)}
						on:keydown={(e) => handlePolicyKeydown(e, rawPolicy)}
					>
						<!-- Policy Header -->
						<div>
							<div class="mb-4 flex items-start justify-between">
								<h2 class="text-xl font-bold text-green-600">
									{rawPolicy.PlanName}
								</h2>
								<div class="flex flex-row items-end gap-1">
									<span class="rounded-full border px-3 py-1 text-xs font-medium bg-green-100 text-green-600 border-green-300">
										Active
									</span>
								</div>
							</div>

							<div class="mb-4">
								<p class="text-sm text-gray-600">
									Policy No: <span class="font-medium text-gray-900">{rawPolicy.PolNo}</span>
								</p>
							</div>

							<!-- Policyholder Section -->
							<div class="mb-4">
								<div class="flex items-center mb-2">
									<UserOutline class="h-4 w-4 text-gray-600 mr-2" />
									<h3 class="text-sm font-semibold text-gray-900">Policyholder</h3>
								</div>
								<div class="ml-6 space-y-1">
									<p class="text-sm text-gray-900 font-medium">{rawPolicy.Name} {rawPolicy.Surname}</p>
									<p class="text-sm text-gray-600">ID: {rawPolicy.CitizenID}</p>
								</div>
							</div>

							<!-- Insurer Section -->
							<div class="mb-4">
								<div class="flex items-center mb-2">
									<BuildingOutline class="h-4 w-4 text-gray-600 mr-2" />
									<h3 class="text-sm font-semibold text-gray-900">Insurer</h3>
								</div>
								<div class="ml-6">
									<p class="text-sm text-gray-900 font-medium">{rawPolicy.InsurerName}</p>
								</div>
							</div>

							<!-- Coverage Period Section -->
							<div class="mb-4">
								<div class="flex items-center mb-2">
									<CalendarMonthOutline class="h-4 w-4 text-gray-600 mr-2" />
									<h3 class="text-sm font-semibold text-gray-900">Coverage Period</h3>
								</div>
								<div class="ml-6 grid grid-cols-2 gap-4">
									<div>
										<p class="text-xs text-gray-500">From</p>
										<p class="text-sm font-medium text-gray-900">{rawPolicy.EffFrom}</p>
									</div>
									<div>
										<p class="text-xs text-gray-500">To</p>
										<p class="text-sm font-medium text-gray-900">{rawPolicy.EffTo}</p>
									</div>
								</div>
							</div>

							<!-- Coverage Benefits Section -->
							<div class="mb-6">
								<div class="flex items-center mb-2">
									<ShieldCheckOutline class="h-4 w-4 text-gray-600 mr-2" />
									<h3 class="text-sm font-semibold text-gray-900">Coverage Benefits</h3>
								</div>
								<div class="ml-6 space-y-2">
									{#if rawPolicy.EvenMBAccident}
										<div>
											<p class="text-sm font-medium text-gray-900">Accident Coverage</p>
											<div class="flex flex-wrap gap-2 mt-1">
												<span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Inpatient Benefits</span>
												<span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Outpatient Benefits</span>
												<span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">PA</span>
											</div>
										</div>
									{/if}
									{#if rawPolicy.EvenMBIllness}
										<div>
											<p class="text-sm font-medium text-gray-900">Illness Coverage</p>
											<div class="flex flex-wrap gap-2 mt-1">
												<span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Dental</span>
												<span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Flexible Benefits</span>
												<span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Inpatient Benefits</span>
												<span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">Outpatient Benefits</span>
											</div>
										</div>
									{/if}
								</div>
							</div>

							<!-- Additional Information -->
							<div class="mb-6 space-y-2 text-sm text-gray-600">
								<div class="flex justify-between">
									<span>Card Type</span>
									<span class="font-medium text-gray-900">{rawPolicy.CardType}</span>
								</div>
								<div class="flex justify-between">
									<span>Member Code</span>
									<span class="font-medium text-gray-900">{rawPolicy.MemberCode}</span>
								</div>
							</div>
						</div>

						<!-- Action Buttons -->
						<div class="flex gap-3 mt-auto">
							<Button
								color="green"
								class="flex-1 bg-green-600 hover:bg-green-700 text-white"
								on:click={(e) => {
									e.stopPropagation();
									handlePolicyClick(rawPolicy);
								}}
							>
								<EyeOutline class="w-4 h-4 mr-2" />
								View Details
							</Button>
							<Button
								color="light"
								class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300"
								on:click={(e) => {
									e.stopPropagation();
									// Handle certificate download
									console.log('Download certificate for policy:', rawPolicy.PolNo);
								}}
							>
								<DownloadOutline class="w-4 h-4 mr-2" />
								Download Certificate
							</Button>
						</div>
					</div>
				{/each}
			{/if}
		</section>
	{/if}
</div>

<!-- Policy Detail Modal with Portal -->
<ModalPortal
	bind:isOpen={policyDetailModalOpen}
	modalId="policy-detail-modal-portal"
>
	<PolicyDetailModal
		bind:isOpen={policyDetailModalOpen}
		{selectedPolicy}
		mockPolicyDetails={{}}
		on:close={() => policyDetailModalOpen = false}
	/>
</ModalPortal>
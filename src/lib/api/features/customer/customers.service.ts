// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl, PLATFORM_IDENTITIES_PAGE_SIZE } from '$src/lib/config';
import type {
    Customer,
    CustomerPlatformIdentity,
    PaginatedPlatformIdentitiesResponse,
    LatestMessagesResponse,
    UnreadCountsResponse,
    MarkAsReadResponse,
    ConversationsResponse,
    MarkMessagesAsReadResponse,
    FocusTicketResponse
} from '$lib/types/customer';

import type { CustomerResponse, CustomerTicketResponse, CustomerPoliciesResponse, RawCustomerPoliciesResponse, CustomerNoteResponse } from "../../types/customer";
import { ApiError } from "../../client/errors";
import { PolicyWorkflowExecutor } from './policy-workflow-executor.service';

export class CustomerService {
    private baseUrl = `${getBackendUrl()}/customer`;
    // private baseUrl = `${PUBLIC_BACKEND_URL.replace(/\/$/, '')}`;


    /**
     * @deprecated Use getCustomersWithFiltersAndOrdering() instead. This method will be removed in a future version.
     * This method is redundant as it calls the same endpoint as getCustomersWithFiltersAndOrdering() without parameters.
     */
    async getAll(token: string): Promise<CustomerResponse> {
        // TODO - Delete this method - it's deprecated in favor of getCustomersWithFiltersAndOrdering
        // console.log(`CustomerService's getAll's baseUrl - ${this.baseUrl}`)

        try {
            const response = await fetch(`${this.baseUrl}/api/customers/paginated/`, {
                // const response = await fetch(`${this.baseUrl}/customer/api/customers/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customers: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customers:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customers'
            };
        }
    }

    async getById(id: string, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/details/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customers: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer'
            };
        }
    }

    async putById(id: string, customerData, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/details/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(customerData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            console.info(`putById method - ${response_json.message}`);
            return {
                customers: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error updating customer:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to updating customer'
            };
        }
    }

    async deleteById(id: string, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            console.info(`deleteById method - ${response_json.message}`);
            return {
                customers: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error deleting customer:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to deleting customer'
            };
        }
    }

    async getCustomerTickets(id: string, token: string): Promise<CustomerTicketResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/tickets/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customer_tickets: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                customer_tickets: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer'
            };
        }
    }

    async getCustomerOwnPolicies(id: string, token: string): Promise<CustomerPoliciesResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/policies/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;
            // console.log(response_json)
            return {
                customer_policies: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                customer_policies: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : "Failed to fetch a customer's policies"
            };
        }
    }

    async getCustomerPoliciesAndClaims(id: string, token: string): Promise<RawCustomerPoliciesResponse> {
        try {
            // Try the new workflow executor first
            console.log(`Attempting workflow execution for customer ${id}`);

            const workflowExecutor = new PolicyWorkflowExecutor();
            const rawPoliciesData = await workflowExecutor.executeWorkflow(id);

            console.log(`Workflow execution successful for customer ${id}`, {
                step3_policies_count: rawPoliciesData.step3_data.ListOfPolicyListSocial.length,
                step4_policy_details_count: rawPoliciesData.step4_data.ListOfPolDet.length,
                step4_claims_count: rawPoliciesData.step4_data.ListOfPolClaim.length
            });

            return {
                customer_policies: [rawPoliciesData],
                res_status: 200
            };

        } catch (workflowError) {
            console.warn('Workflow execution failed, falling back to API call:', workflowError);

            // Fallback to existing API call - but we need to return raw format
            // For now, throw the error since we're removing transformation
            throw new Error(`Policy workflow execution failed: ${workflowError instanceof Error ? workflowError.message : 'Unknown error'}`);
        }
    }

    async getCustomerClaims(id: string, token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/claims/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;
            return {
                customer_claims: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer claims:', error);
            return {
                customer_claims: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : "Failed to fetch customer's claims"
            };
        }
    }

    async getPolicyDetails(policyId: string, token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/policies/${policyId}/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;
            return {
                policy: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching policy details:', error);
            return {
                policy: null,
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : "Failed to fetch policy details"
            };
        }
    }

    async getClaimDetails(claimId: string, token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/claims/${claimId}/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;
            return {
                claim: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching claim details:', error);
            return {
                claim: null,
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : "Failed to fetch claim details"
            };
        }
    }

    async getCustomerNotes(id: string, token: string): Promise<CustomerNoteResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/notes/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customer_notes: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer:', error);
            return {
                customer_notes: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : "Failed to fetch a customer's policies"
            };
        }
    }

    // ========================= Customer Tag Methods =========================
    // GET /user/api/customer-tag/
    async getAllTags(token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customer-tag/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customers: response_json,
                res_status: res_status
            };

        } catch (error) {
            console.error('Error fetching customer tags:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer tags'
            };
        }
    }

    async createCustomerTag(tagData: { name: string }, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customer-tag/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tagData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customers: [response_json.data],
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error creating customer tag:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to create customer tag'
            };
        }
    }

    async deleteCustomerTag(tagId: string, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customer-tag/${tagId}/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customers: [],
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error deleting customer tag:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to delete customer tag'
            };
        }
    }

    async updateCustomerTag(tagId: string, tagData: { name: string }, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customer-tag/${tagId}/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tagData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                customers: [response_json.data],
                res_status: res_status,
                res_msg: response_json.message
            };
        } catch (error) {
            console.error('Error updating customer tag:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to update customer tag'
            };
        }
    }

    async assignTagsById(id: string, tagData: { tag_ids: (string | number)[] }, token: string): Promise<CustomerResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/tags/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tagData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            console.info(`assignTagsById method - ${response_json.message}`);
            return {
                customers: response_json.data,
                res_status: res_status,
                res_msg: response_json.message
            };

        } catch (error) {
            console.error('Error assigning tags to user:', error);
            return {
                customers: [],
                res_status: error.status,
                error_msg: error instanceof Error ? `Error's message - ${error.message}` : 'Failed to assign tags to user'
            };
        }
    }

    // Fetch memories for a customer
    async getMemoryById(id: string, token: string): Promise<{
        memories: Array<{ id: number; detail_en: string; detail_th: string; created_on: string }>;
        res_status: number;
        error_msg?: string;
    }> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/memories/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
            );

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(JSON.stringify(errorData), response.status);
            }

            const data = await response.json();
            return {
                memories: data,
                res_status: response.status
            };
        } catch (error: any) {
            console.error('Error fetching customer memories:', error);
            return {
                memories: [],
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch memories'
            };
        }
    }

    // Delete a memory for a customer
    async deleteMemoryById(memoryId: string, token: string): Promise<{ res_status: number; res_msg?: string; error_msg?: string }> {
        try {
            const response = await fetch(
                `${this.baseUrl}/api/customers/memories/${memoryId}/`,
                {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(JSON.stringify(errorData), response.status);
            }

            const responseJson = await response.json();
            return {
                res_status: response.status,
                res_msg: responseJson.message
            };
        } catch (error: any) {
            console.error('Error deleting customer memory:', error);
            return {
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to delete memory'
            };
        }
    }

    // Delete a memory for a customer
    async chatCenterCustomerList(token: string): Promise<{ res_status: number; res_msg?: string; error_msg?: string }> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/`,
                {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseJson = await response.json();
            return {
                res_status: response.status,
                res_msg: responseJson.message
            };
        } catch (error: any) {
            console.error('Error deleting customer memory:', error);
            return {
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer list'
            };
        }
    }

    async getPlatformInfo(customerId: number, platformId: number, token: string): Promise<CustomerPlatformIdentity> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${customerId}/platform-identities/${platformId}/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }

            return await response.json();
        } catch (error) {
            console.error('Error fetching platform info:', error);
            throw error;
        }
    }

    async getCustomerTicketAnalyses(id: string, token: string): Promise<{
        ticket_analyses: any[];
        res_status: number;
        error_msg?: string;
    }> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${id}/ticket-analyses/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(JSON.stringify(errorData), response.status);
            }

            const response_json = await response.json();
            // console.log('customer.service.ts: getCustomerTicketAnalyses(): Full ticket analyses data:', response_json);

            return {
                ticket_analyses: response_json,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error fetching customer ticket analyses:', error);
            return {
                ticket_analyses: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customer ticket analyses'
            };
        }
    }

    async getCustomersWithFiltersAndOrdering(
        token: string,
        filters: {
            search?: string;
            search_fields?: string;
            tags?: string;
            platforms?: string;
            page?: number;
            limit?: number;
        } = {},
        ordering: string = 'customer_id'
    ): Promise<CustomerResponse> {
        try {
            // Build query parameters
            const params = new URLSearchParams();

            // Add ordering (always send ordering parameter to ensure consistency)
            if (ordering) {
                params.append('ordering', ordering);
            }

            // Add filters
            if (filters.search && filters.search.trim()) {
                params.append('search', filters.search);
            }
            if (filters.search_fields && filters.search_fields.trim()) {
                params.append('search_fields', filters.search_fields);
            }
            if (filters.tags && filters.tags.trim()) {
                params.append('tags', filters.tags);
            }
            if (filters.platforms && filters.platforms.trim()) {
                params.append('platforms', filters.platforms);
            }
            if (filters.page && filters.page > 1) {
                params.append('page', filters.page.toString());
            }
            if (filters.limit) {
                params.append('limit', filters.limit.toString());
            }

            const queryString = params.toString();
            const url = `${this.baseUrl}/api/customers/paginated/${queryString ? '?' + queryString : ''}`;

            // console.warn('customer.service.ts: getCustomersWithFiltersAndOrdering(): Calling ', url);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.message || 'Failed to fetch customers',
                    response.status
                );
            }

            const response_json = await response.json();

            // response_json.results.forEach((customer, index) => {
            //     console.log(`customer.service.ts: getCustomersWithFiltersAndOrdering(): Customer ${index + 1}:`, {
            //         // name: customer.name,
            //         // customer_id: customer.customer_id,
            //         platforms: customer.platforms,
            //         // tags: customer.tags,
            //         // tag_count: customer.tag_count
            //     });
            // });

            return {
                customers: response_json, 
                res_status: response.status
            };
        } catch (error) {
            console.error('customer.service.ts: getCustomersWithFiltersAndOrdering(): Error fetching customers:', error);
            return {
                customers: [],
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch customers'
            };
        }
    }

    async getFilterTags(token: string): Promise<any> {
        try {
            const response = await fetch(`${this.baseUrl}/api/filters/tags/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.message || 'Failed to fetch tags',
                    response.status
                );
            }

            const response_json = await response.json();

            return {
                data: response_json.tags || [],
                res_status: response.status
            };
        } catch (error) {
            console.error('Error fetching filter tags:', error);
            return {
                data: [],
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch tags'
            };
        }
    }

    async getFilterPlatforms(token: string): Promise<any> {
			try {
				const response = await fetch(`${this.baseUrl}/api/filters/platforms/`, {
					method: 'GET',
					headers: {
						Authorization: `Bearer ${token}`,
						'Content-Type': 'application/json'
					}
				});

				if (!response.ok) {
					const errorData = await response.json().catch(() => ({}));
					throw new ApiError(errorData.message || 'Failed to fetch platforms', response.status);
				}

				const response_json = await response.json();

				// console.warn('customer.service.ts: getFilterPlatforms():', response_json.platforms);

				return {
					data: response_json.platforms || [],
					res_status: response.status
				};
			} catch (error) {
				console.error('Error fetching filter platforms:', error);
				return {
					data: [],
					res_status: error instanceof ApiError ? error.status || 500 : 500,
					error_msg: error instanceof Error ? error.message : 'Failed to fetch platforms'
				};
			}

			// try {
			//     // For now, return hardcoded platform options since platforms are a fixed set
			//     // This can be changed to an API call if backend provides a platforms endpoint
			//     const platformOptions = [
			//         { id: 'LINE', name: 'LINE', color: 'green' },
			//         { id: 'FACEBOOK', name: 'FACEBOOK', color: 'blue' },
			//         { id: 'TELEPHONE', name: 'TELEPHONE', color: 'gray' },
			//         { id: 'WHATSAPP', name: 'WHATSAPP', color: 'green' },
			//         { id: 'INSTAGRAM', name: 'INSTAGRAM', color: 'pink' },
			//         { id: 'TELEGRAM', name: 'TELEGRAM', color: 'blue' }
			//     ];

			//     return {
			//         data: platformOptions,
			//         res_status: 200
			//     };
			// } catch (error) {
			//     console.error('Error fetching filter platforms:', error);
			//     return {
			//         data: [],
			//         res_status: 500,
			//         error_msg: error instanceof Error ? error.message : 'Failed to fetch platforms'
			//     };
			// }
    }

    async getCustomerConsentById(id: string, token: string): Promise<any> {
        try {
            const response = await fetch(`${getBackendUrl()}/consent/api/consent/customer/${id}/summary/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            // console.log('customer.service.ts: getCustomerConsentById(): Consent data:', response_json);
            
            return {
                consent_data: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching user consent:', error);
            return {
                consent_data: null,
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch user consent'
            };
        }
    }

    async exportCustomerConversations(
        customerId: string, 
        access_token: string, 
        options: {
            format: string;
            include_deleted_messages: boolean;
            include_attachments: boolean;
            store_permanently: boolean;
        }
    ): Promise<Blob> {
        try {
            const response = await fetch(`${getBackendUrl()}/export/customers/${customerId}/export-conversations-direct/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(options)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            return await response.blob();
        } catch (error) {
            console.error('Error exporting customer conversations:', error);
            throw error instanceof ApiError ? error : new ApiError(
                error instanceof Error ? error.message : 'Failed to export customer conversations',
                500
            );
        }
    }

    // ========================= Platform Identity Methods =========================

    /**
     * Get platform identities for all tabs with pagination support
     * This method fetches data for multiple tabs at once, similar to the page server logic
     */
    async getPlatformIdentitiesForAllTabs(
        token: string,
        options: {
            currentUser: string;
            search?: string;
            platform?: string;
            pageSize?: number;
        }
    ): Promise<{
        tabPaginationData: Record<string, any>;
        res_status: number;
        error_msg?: string;
    }> {
        try {
            const tabs = ['my-assigned', 'my-closed', 'open', 'others-assigned'];
            const pageSize = options.pageSize || PLATFORM_IDENTITIES_PAGE_SIZE;

            // console.log('[DEBUG] getPlatformIdentitiesForAllTabs: Starting fetch for all tabs', {
            //     tabs,
            //     pageSize,
            //     options
            // });

            // Load initial data for all tabs (2 items each by default)
            const tabPromises = tabs.map(async (tab) => {
                // console.log(`[DEBUG] getPlatformIdentitiesForAllTabs: Fetching data for tab "${tab}"`);

                const result = await this.getPlatformIdentitiesWithFilters(token, {
                    tab,
                    currentUser: options.currentUser,
                    search: options.search,
                    platform: options.platform,
                    page: 1,
                    pageSize
                });

                // console.log(`[DEBUG] getPlatformIdentitiesForAllTabs: Tab "${tab}" result:`, {
                //     status: result.res_status,
                //     resultCount: result.results?.length || 0,
                //     hasNext: !!result.next,
                //     totalCount: result.count || 0,
                //     platformIds: result.results?.map(p => p.id) || []
                // });

                if (result.res_status === 200) {
                    // Check for duplicate IDs in this tab's results
                    if (result.results && result.results.length > 0) {
                        const ids = result.results.map(p => p.id);
                        const uniqueIds = [...new Set(ids)];
                        if (ids.length !== uniqueIds.length) {
                            console.error(`[ERROR] getPlatformIdentitiesForAllTabs: Duplicate platform IDs in tab "${tab}":`, {
                                allIds: ids,
                                duplicates: ids.filter((id, index) => ids.indexOf(id) !== index)
                            });
                        }
                    }

                    return {
                        tab,
                        platforms: result.results || [],
                        hasMore: !!result.next,
                        total: result.count || 0
                    };
                } else {
                    console.error(`[ERROR] getPlatformIdentitiesForAllTabs: Failed to load ${tab} tab data:`, result.error_msg);
                    return {
                        tab,
                        platforms: [],
                        hasMore: false,
                        total: 0
                    };
                }
            });

            const tabResults = await Promise.all(tabPromises);
            const tabPaginationData: Record<string, any> = {};

            tabResults.forEach(result => {
                tabPaginationData[result.tab] = {
                    platforms: result.platforms,
                    hasMore: result.hasMore,
                    total: result.total
                };
            });

            return {
                tabPaginationData,
                res_status: 200
            };
        } catch (error) {
            console.error('Error fetching platform identities for all tabs:', error);
            return {
                tabPaginationData: {},
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to load platform identities for all tabs'
            };
        }
    }

    /**
     * Get platform identities with pagination-aware polling support
     * This method respects the current pagination state and applied filters
     */
    async getPlatformIdentitiesForPolling(
        token: string,
        options: {
            tab: string;
            currentUser: string;
            search?: string;
            platform?: string;
            loadedPages: number; // Number of pages currently loaded
            pageSize?: number;
        }
    ): Promise<PaginatedPlatformIdentitiesResponse> {
        try {
            const pageSize = options.pageSize || PLATFORM_IDENTITIES_PAGE_SIZE;
            const allResults: any[] = [];
            let totalCount = 0;
            let hasNext = false;

            // Fetch all currently loaded pages to maintain the user's view state
            for (let page = 1; page <= options.loadedPages; page++) {
                const result = await this.getPlatformIdentitiesWithFilters(token, {
                    tab: options.tab,
                    currentUser: options.currentUser,
                    search: options.search,
                    platform: options.platform,
                    page,
                    pageSize
                });

                if (result.res_status === 200) {
                    allResults.push(...(result.results || []));
                    totalCount = result.count || 0;

                    // Check if there are more pages beyond what we've loaded
                    if (page === options.loadedPages) {
                        hasNext = !!result.next;
                    }
                } else {
                    console.error(`Error loading page ${page} for polling:`, result.error_msg);
                    break;
                }
            }

            return {
                results: allResults,
                count: totalCount,
                next: hasNext ? 'has_more' : undefined,
                previous: undefined,
                res_status: 200
            };
        } catch (error) {
            console.error('Error fetching platform identities for polling:', error);
            return {
                results: [],
                count: 0,
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to load platform identities for polling'
            };
        }
    }

    async getCustomerDetails(customerId: number, token: string): Promise<{ customer: Customer | null; res_status: number; error_msg?: string }> {
        try {
            const response = await fetch(`${this.baseUrl}/api/customers/${customerId}/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const customer = await response.json();
            return {
                customer,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error loading customer details:', error);
            return {
                customer: null,
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to load customer details'
            };
        }
    }

    // async getPlatformIdentitiesPaginated(
    //     page: number,
    //     token: string,
    //     pageSize: number = 2,
    //     tab?: string,
    //     currentUser?: string
    // ): Promise<PaginatedPlatformIdentitiesResponse> {
    //     try {
    //         // Build query parameters
    //         const params = new URLSearchParams();
    //         params.append('page', page.toString());
    //         params.append('page_size', pageSize.toString());

    //         if (tab) params.append('tab', tab);
    //         if (currentUser) params.append('current_user', currentUser);

    //         const response = await fetch(`${this.baseUrl}/api/platform-identities/?${params.toString()}`, {
    //             method: 'GET',
    //             headers: {
    //                 'Authorization': `Bearer ${token}`,
    //                 'Content-Type': 'application/json'
    //             }
    //         });

    //         if (!response.ok) {
    //             const errorData = await response.json().catch(() => ({}));
    //             throw new ApiError(
    //                 JSON.stringify(errorData),
    //                 response.status
    //             );
    //         }

    //         const data = await response.json();
    //         return {
    //             results: data.results || [],
    //             count: data.count || 0,
    //             next: data.next,
    //             previous: data.previous,
    //             res_status: response.status
    //         };
    //     } catch (error) {
    //         console.error('Error loading paginated platform identities:', error);
    //         return {
    //             results: [],
    //             count: 0,
    //             res_status: error instanceof ApiError ? (error.status || 500) : 500,
    //             error_msg: error instanceof Error ? error.message : 'Failed to load platform identities'
    //         };
    //     }
    // }

    // async getLatestPlatformIdentities(token: string): Promise<PaginatedPlatformIdentitiesResponse> {
    //     try {
    //         const response = await fetch(`${this.baseUrl}/api/platform-identities/`, {
    //             method: 'GET',
    //             headers: {
    //                 'Authorization': `Bearer ${token}`,
    //                 'Content-Type': 'application/json'
    //             }
    //         });

    //         if (!response.ok) {
    //             const errorData = await response.json().catch(() => ({}));
    //             throw new ApiError(
    //                 JSON.stringify(errorData),
    //                 response.status
    //             );
    //         }

    //         const data = await response.json();
    //         return {
    //             results: data.results || [],
    //             count: data.count || 0,
    //             next: data.next,
    //             previous: data.previous,
    //             res_status: response.status
    //         };
    //     } catch (error) {
    //         console.error('Error loading latest platform identities:', error);
    //         return {
    //             results: [],
    //             count: 0,
    //             res_status: error instanceof ApiError ? (error.status || 500) : 500,
    //             error_msg: error instanceof Error ? error.message : 'Failed to load latest platform identities'
    //         };
    //     }
    // }

    /**
     * Get platform identities with filtering and pagination support
     * This method supports tab-based filtering, search, platform filtering, and pagination
     */
    async getPlatformIdentitiesWithFilters(
        token: string,
        options: {
            tab?: string;
            currentUser?: string;
            search?: string;
            platform?: string;
            page?: number;
            pageSize?: number;
        } = {}
    ): Promise<PaginatedPlatformIdentitiesResponse> {
        try {
            const url = new URL(`${this.baseUrl}/api/platform-identities/`);

            // Add query parameters
            if (options.tab) url.searchParams.set('tab', options.tab);
            if (options.currentUser) url.searchParams.set('current_user', options.currentUser);
            if (options.search) url.searchParams.set('search', options.search);
            if (options.platform) url.searchParams.set('platform', options.platform);
            if (options.page) url.searchParams.set('page', options.page.toString());
            if (options.pageSize) url.searchParams.set('page_size', options.pageSize.toString());

            // console.log('[DEBUG] getPlatformIdentitiesWithFilters: Making API call', {
            //     url: url.toString(),
            //     options,
            //     expectedPageSize: options.pageSize || 'default'
            // });

            const response = await fetch(url.toString(), {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('[ERROR] getPlatformIdentitiesWithFilters: API request failed', {
                    status: response.status,
                    statusText: response.statusText,
                    errorData
                });
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const data = await response.json();

            // console.log('[DEBUG] getPlatformIdentitiesWithFilters: API response received', {
            //     resultCount: data.results?.length || 0,
            //     totalCount: data.count || 0,
            //     hasNext: !!data.next,
            //     hasPrevious: !!data.previous,
            //     platformIds: data.results?.map((p: any) => p.id) || [],
            //     requestedPage: options.page || 1,
            //     requestedPageSize: options.pageSize || 'default'
            // });

            // Check for duplicate IDs in API response
            if (data.results && data.results.length > 0) {
                const ids = data.results.map((p: any) => p.id);
                const uniqueIds = [...new Set(ids)];
                if (ids.length !== uniqueIds.length) {
                    console.error('[ERROR] getPlatformIdentitiesWithFilters: Duplicate platform IDs in API response:', {
                        tab: options.tab,
                        page: options.page,
                        allIds: ids,
                        duplicates: ids.filter((id: any, index: number) => ids.indexOf(id) !== index)
                    });
                }
            }

            return {
                results: data.results || [],
                count: data.count || 0,
                next: data.next,
                previous: data.previous,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error fetching platform identities with filters:', error);
            return {
                results: [],
                count: 0,
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to load platform identities with filters'
            };
        }
    }

    // async getLatestMessages(platformIds: number[], token: string): Promise<LatestMessagesResponse> {
    //     try {
    //         const response = await fetch(
    //             `${this.baseUrl}/api/platform-messages/?platform_ids=${platformIds.join(',')}`,
    //             {
    //                 method: 'GET',
    //                 headers: {
    //                     'Authorization': `Bearer ${token}`,
    //                     'Content-Type': 'application/json'
    //                 }
    //             }
    //         );

    //         if (!response.ok) {
    //             const errorData = await response.json().catch(() => ({}));
    //             throw new ApiError(
    //                 JSON.stringify(errorData),
    //                 response.status
    //             );
    //         }

    //         const data = await response.json();
    //         return {
    //             messages: data,
    //             res_status: response.status
    //         };
    //     } catch (error) {
    //         console.error('Error loading latest messages:', error);
    //         return {
    //             messages: {},
    //             res_status: error instanceof ApiError ? (error.status || 500) : 500,
    //             error_msg: error instanceof Error ? error.message : 'Failed to load latest messages'
    //         };
    //     }
    // }

    // async getUnreadCounts(platformIds: number[], token: string): Promise<UnreadCountsResponse> {
    //     try {
    //         const response = await fetch(
    //             `${this.baseUrl}/api/platform-unread-counts/?platform_ids=${platformIds.join(',')}`,
    //             {
    //                 method: 'GET',
    //                 headers: {
    //                     'Authorization': `Bearer ${token}`,
    //                     'Content-Type': 'application/json'
    //                 }
    //             }
    //         );

    //         if (!response.ok) {
    //             const errorData = await response.json().catch(() => ({}));
    //             throw new ApiError(
    //                 JSON.stringify(errorData),
    //                 response.status
    //             );
    //         }

    //         const data = await response.json();
    //         return {
    //             unread_counts: data,
    //             res_status: response.status
    //         };
    //     } catch (error) {
    //         console.error('Error loading unread counts:', error);
    //         return {
    //             unread_counts: {},
    //             res_status: error instanceof ApiError ? (error.status || 500) : 500,
    //             error_msg: error instanceof Error ? error.message : 'Failed to load unread counts'
    //         };
    //     }
    // }

    async markAllMessagesAsRead(customerId: number, platformId: number, token: string): Promise<MarkAsReadResponse> {
        try {
            const response = await fetch(
                `${this.baseUrl}/api/customers/${customerId}/platforms/${platformId}/readAll/`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message_ids: []
                    })
                }
            );

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseJson = await response.json();
            return {
                res_status: response.status,
                res_msg: responseJson.message || 'Messages marked as read successfully'
            };
        } catch (error) {
            console.error('Error marking messages as read:', error);
            return {
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to mark messages as read'
            };
        }
    }

    async getConversationsForTickets(customerId: number, platformId: number, ticketIds: number[], token: string): Promise<ConversationsResponse> {
        try {
            // Build query parameters for ticket IDs
            const params = new URLSearchParams();
            ticketIds.forEach(id => params.append('ticket_ids[]', id.toString()));
            params.append('limit', '100'); // Load more messages for multiple tickets

            const response = await fetch(
                `${this.baseUrl}/api/customers/${customerId}/platforms/${platformId}/conversations/?${params.toString()}`,
                {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const data = await response.json();
            return {
                messages: data.messages || [],
                has_more: data.has_more || false,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error loading conversations for tickets:', error);
            return {
                messages: [],
                has_more: false,
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to load conversations for tickets'
            };
        }
    }

    async markMessagesAsRead(customerId: number, platformId: number, messageIds: number[], token: string): Promise<MarkMessagesAsReadResponse> {
        try {
            const response = await fetch(
                `${this.baseUrl}/api/customers/${customerId}/platforms/${platformId}/read/`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message_ids: messageIds
                    })
                }
            );

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseJson = await response.json();
            return {
                res_status: response.status,
                res_msg: responseJson.message || 'Messages marked as read successfully'
            };
        } catch (error) {
            console.error('Error marking messages as read:', error);
            return {
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to mark messages as read'
            };
        }
    }

    async getFocusTicketInitialTickets(ticketId: number, token: string): Promise<FocusTicketResponse> {
        try {
            const response = await fetch(
                `${this.baseUrl}/api/customers/focus-ticket/${ticketId}/initial-tickets/`,
                {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const data = await response.json();
            return {
                tickets: data.tickets || [],
                has_more_older: data.has_more_older || false,
                has_more_newer: data.has_more_newer || false,
                initial_older_count: data.initial_older_count || 0,
                initial_newer_count: data.initial_newer_count || 0,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error fetching focus ticket initial tickets:', error);
            return {
                tickets: [],
                has_more_older: false,
                has_more_newer: false,
                initial_older_count: 0,
                initial_newer_count: 0,
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch focus ticket initial tickets'
            };
        }
    }

    async getFocusTicketMoreTickets(
        ticketId: number,
        windowOffset: number,
        limit: number = 10,
        initialOlderCount: number,
        initialNewerCount: number,
        token: string
    ): Promise<FocusTicketResponse> {
        try {
            const params = new URLSearchParams({
                window_offset: windowOffset.toString(),
                limit: limit.toString(),
                initial_older_count: initialOlderCount.toString(),
                initial_newer_count: initialNewerCount.toString()
            });

            const response = await fetch(
                `${this.baseUrl}/api/customers/focus-ticket/${ticketId}/more-tickets/?${params.toString()}`,
                {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const data = await response.json();
            return {
                tickets: data.tickets || [],
                has_more_older: data.has_more_older || false,
                has_more_newer: data.has_more_newer || false,
                initial_older_count: data.initial_older_count || 0,
                initial_newer_count: data.initial_newer_count || 0,
                res_status: response.status
            };
        } catch (error) {
            console.error('Error fetching focus ticket more tickets:', error);
            return {
                tickets: [],
                has_more_older: false,
                has_more_newer: false,
                initial_older_count: 0,
                initial_newer_count: 0,
                res_status: error instanceof ApiError ? (error.status || 500) : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch focus ticket more tickets'
            };
        }
    }
}